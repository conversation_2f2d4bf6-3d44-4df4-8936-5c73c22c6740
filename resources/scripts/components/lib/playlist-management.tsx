import { useState, FormEvent } from "react";
import { useModal, useZiggy } from "@/hooks";
import { Button, InputField, Modal } from "./ui";
import { fetchApi } from "@/utils";
import { router } from "@inertiajs/react";

interface CreatePlaylistModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: (playlist: any) => void;
}

export function CreatePlaylistModal({ isOpen, onClose, onSuccess }: CreatePlaylistModalProps) {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        name: "",
        description: "",
        is_public: true,
    });
    const [errors, setErrors] = useState<Record<string, string>>({});
    const route = useZiggy();

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setErrors({});

        try {
            const response = await fetchApi(route("app.playlists.create"), {
                method: "POST",
                body: JSON.stringify(formData),
            });

            if (onSuccess) {
                onSuccess(response);
            }

            // Reset form
            setFormData({
                name: "",
                description: "",
                is_public: true,
            });

            onClose();
            router.reload();
        } catch (error: any) {
            if (error.errors) {
                setErrors(error.errors);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field: string, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: "" }));
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} title="Create New Playlist">
            <form onSubmit={handleSubmit} className="space-y-4">
                <InputField
                    label="Playlist Name"
                    name="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter playlist name"
                    errors={errors}
                    required
                />

                <div>
                    <label className="block text-sm font-medium mb-2">
                        Description
                    </label>
                    <textarea
                        name="description"
                        value={formData.description}
                        onChange={(e) => handleInputChange("description", e.target.value)}
                        placeholder="Enter playlist description (optional)"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        rows={3}
                    />
                    {errors.description && (
                        <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                    )}
                </div>

                <div className="flex items-center space-x-2">
                    <input
                        type="checkbox"
                        id="is_public"
                        checked={formData.is_public}
                        onChange={(e) => handleInputChange("is_public", e.target.checked)}
                        className="rounded border-gray-300 dark:border-gray-600"
                    />
                    <label htmlFor="is_public" className="text-sm">
                        Make playlist public
                    </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onClose}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        variant="primary"
                        disabled={loading || !formData.name.trim()}
                    >
                        {loading ? "Creating..." : "Create Playlist"}
                    </Button>
                </div>
            </form>
        </Modal>
    );
}

interface AddToPlaylistModalProps {
    isOpen: boolean;
    onClose: () => void;
    articleId: string;
    userPlaylists?: any[];
}

export function AddToPlaylistModal({ 
    isOpen, 
    onClose, 
    articleId, 
    userPlaylists = [] 
}: AddToPlaylistModalProps) {
    const [loading, setLoading] = useState(false);
    const [selectedPlaylist, setSelectedPlaylist] = useState("");
    const route = useZiggy();

    const handleAddToPlaylist = async () => {
        if (!selectedPlaylist) return;

        setLoading(true);
        try {
            await fetchApi(route("app.playlists.add-song", { playlist: selectedPlaylist }), {
                method: "POST",
                body: JSON.stringify({ article_id: articleId }),
            });

            onClose();
            router.reload();
        } catch (error) {
            console.error("Failed to add song to playlist:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose} title="Add to Playlist">
            <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium mb-2">
                        Select Playlist
                    </label>
                    <select
                        value={selectedPlaylist}
                        onChange={(e) => setSelectedPlaylist(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    >
                        <option value="">Choose a playlist...</option>
                        {userPlaylists.map((playlist) => (
                            <option key={playlist.id} value={playlist.id}>
                                {playlist.name}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onClose}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="button"
                        variant="primary"
                        onClick={handleAddToPlaylist}
                        disabled={loading || !selectedPlaylist}
                    >
                        {loading ? "Adding..." : "Add to Playlist"}
                    </Button>
                </div>
            </div>
        </Modal>
    );
}

export function usePlaylistManagement() {
    const createModal = useModal();
    const addToPlaylistModal = useModal();

    return {
        createModal,
        addToPlaylistModal,
        CreatePlaylistModal,
        AddToPlaylistModal,
    };
}

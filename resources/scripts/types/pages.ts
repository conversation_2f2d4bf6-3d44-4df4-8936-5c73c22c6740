import { CanPolicy, Paginate } from ".";
import {
    Article,
    Channel,
    Competition,
    Genre,
    Notification,
    Playlist,
    User,
    UserRole,
} from "./models";

type FlashMessage = {
    message?: string;
    error?: string;
    success?: string;
};

type SSOEnabled = {
    enabled?: boolean;
};

type SSO = {
    google?: SSOEnabled;
};

export type SharedAppData = {
    appName: string;
    authUser?: User;
    notifications: Notification[];
    ziggy: any;
    flash?: FlashMessage;
    isImpersonating: boolean;
    hlsEnabled: boolean;
    sso?: SSO;
};

export type AccountPageProps = CanPolicy<"create_artist_request"> &
    SharedAppData & {
        artist_roles: UserRole[];
    };

export type ResetPasswordPageProps = SharedAppData & {
    token: string;
    email?: string | null;
};

export type DiscoverSourceType =
    | "global_popular_guest"
    | "no_recommendations_available"
    | "personalized_playlists"
    | "global_popular_fallback"
    | "no_recommendations_available";

export type DiscoverPageProps = SharedAppData & {
    playlists: Playlist[] | Record<string, Playlist>;
    source: DiscoverSourceType;
    recently_played: Channel[];
    top_charts: Article[];
    top_artists: User[];
};

export type ArtistsPageProps = SharedAppData & {
    artists: Paginate<User>;
};

export type ArtistShowPageProps = SharedAppData & {
    artist: User & {
        active_competitions?: Array<{
            id: string;
            name: string;
            type: string;
            stage: number;
            status: string;
            entry_date: string;
            phase_status: string;
        }>;
    };
    channels: Paginate<Channel>;
    featurings: Paginate<Channel>;
    topchart_articles: Article[];
    followed: boolean;
};

export type ChannelsPageProps = SharedAppData & {
    channels: Paginate<Channel>;
};

export type ChannelShowPageProps = SharedAppData & {
    channel: Channel;
    more_channels: Paginate<Channel>;
    articles: Article[];
};

export type PlaylistShowPageProps = SharedAppData & {
    playlist: Playlist;
    articles: Article[];
};

export type SearchPageProps = SharedAppData & {
    genres: Paginate<Genre>;
    search: {
        channels: Channel[];
        articles: Article[];
        artists: User[];
        genres: Genre[];
    } | null;
};

export type GenreShowPageProps = SharedAppData & {
    genre: Genre;
    channels: Paginate<Channel>;
    articles: Paginate<Article>;
};

export type FavoritesPageProps = SharedAppData & {
    followings: Paginate<User>;
    channels: Paginate<Channel>;
    articles: Paginate<Article>;
};

export type LibraryPageProps = SharedAppData & {
    user_playlists: Paginate<Playlist>;
    liked_playlists: Paginate<Playlist>;
    recent_playlists: Paginate<Playlist>;
};

export type VotingPageProps = SharedAppData & {
    competitions: Competition[];
    similar_votes: User[];
    recently_voted: User[];
};

export type LeaderboardPageProps = SharedAppData & {
    competition: Competition;
    leaderboard: Paginate<User & { votes_count: number }>;
};

export type EntryFormPageProps = SharedAppData & {
    competition: Competition;
    meetsRequirements: boolean;
};

export type ArtistProfilePageProps = SharedAppData & {
    competition: Competition;
    artist: User;
    voteCount: number;
    hasVoted: boolean;
    entry: any;
};

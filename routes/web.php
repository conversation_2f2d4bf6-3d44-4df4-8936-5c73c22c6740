<?php

use App\Http\Controllers;
use App\Http\Controllers\API\OnboardingController;
use App\Http\Controllers\AppWeb;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LoginViewController;
use App\Http\Controllers\MetricsController;
use App\Http\Controllers\ResetPasswordController;
use App\Http\Controllers\Streaming;
use App\Http\Controllers\Streaming\PlayerController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::impersonate();

// Prometheus metrics endpoint
Route::get('/metrics', MetricsController::class)
    ->middleware('metrics.access')
    ->name('metrics');

Route::middleware('auth')
    ->group(function () {
        Route::get('audio/{file}', Streaming\AudioRangeStreamController::class)
            ->name('audio');

        Route::get('hls/{file}/{filename}', Streaming\AudioHlsStreamController::class)
            ->where(['file' => '[a-z0-9]+', 'filename' => '.+'])
            ->name('hls');

        Route::post('player/next-dj-track', [PlayerController::class, 'getNextDjTrack'])
            ->name('player.next-dj-track');
    });

Route::get('/', HomeController::class);

Route::get('/reset-password/{token}', ResetPasswordController::class)
    ->middleware('guest')
    ->name('password.reset');

Route::prefix('socialite')
    ->name('socialite.')
    ->group(function () {
        Route::get('/{provider}/redirect', [Controllers\SocialiteController::class, 'redirect'])->name('redirect');
        Route::get('/{provider}/callback', [Controllers\SocialiteController::class, 'callback'])->name('callback');
    });

Route::get('/app/login', LoginViewController::class)->name('login');

Route::prefix('app')
    ->name('app.')
    ->group(function () {
        // This cannot be protected by auth middleware, cause is the default
        // unauthenticated route redirection
        Route::get('/', AppWeb\DiscoverController::class)
            ->name('discover');

        // Route::get('/podcasts', AppWeb\PodcastsController::class)->name('podcasts');

        Route::get('/favorites', AppWeb\FavoritesController::class)
            ->name('favorites');

        Route::get('/favorites/articles', [AppWeb\FavoritesController::class, 'favoritesArticles'])
            ->middleware(['auth'])
            ->name('favorites.articles');

        // User Library
        Route::get('/library', AppWeb\LibraryController::class)->name('library');
        Route::get('/library/playlists', [AppWeb\LibraryController::class, 'playlists'])
            ->middleware(['auth'])
            ->name('library.playlists');

        // Messages
        Route::middleware(['auth'])->prefix('messages')->group(function () {
            Route::get('/', [AppWeb\MessagesController::class, 'index'])->name('messages.index');
            Route::get('/unread-count', [AppWeb\MessagesController::class, 'unreadCount'])->name('messages.unread-count');
            Route::post('/', [AppWeb\MessagesController::class, 'store'])->name('messages.store');
            Route::get('/start/{user}', [AppWeb\MessagesController::class, 'startConversation'])->name('messages.start');
            Route::get('/{conversation}', [AppWeb\MessagesController::class, 'show'])->name('messages.show');
            Route::post('/{conversation}/mark-read', [AppWeb\MessagesController::class, 'markAsRead'])->name('messages.mark-read');
        });

        // Search and Genre
        Route::prefix('search')
            ->group(function () {
                Route::get('/', AppWeb\SearchController::class)->name('search');

                Route::get('g/{genre}', [AppWeb\GenresController::class, 'show'])->name('search.genres.show');

                Route::get('g/{genre}/articles', [AppWeb\GenresController::class, 'genreArticles'])
                    ->middleware(['auth'])
                    ->name('search.genres.articles');
            });

        // Artists
        Route::prefix('artists')
            ->group(function () {
                // Route::get('/', AppWeb\ArtistesController::class)->name('artists');

                Route::get('/{user}', [AppWeb\ArtistesController::class, 'show'])->name('artists.show');

                // XHR request
                Route::get('/{user}/top-charts', [AppWeb\ArtistesController::class, 'getPopularArticles'])
                    ->middleware(['auth'])
                    ->name('artists.top-charts');
            });

        // Articles
        Route::prefix('articles')
            ->name('articles.')
            ->group(function () {
                // XHR request
                Route::get('/{article}/view', [AppWeb\ArticlesController::class, 'view'])
                    ->middleware(['auth'])
                    ->name('view');
            });

        // Channels
        Route::prefix('channels')
            ->group(function () {
                // Route::get('/', AppWeb\ChannelsController::class)->name('channels');

                Route::get('/{channel}', [AppWeb\ChannelsController::class, 'show'])
                    ->name('channels.show');

                // XHR request
                Route::get('/{channel}/articles', [AppWeb\ChannelsController::class, 'articles'])
                    ->middleware(['auth'])
                    ->name('channels.articles');

                Route::get('/{channel}/download', [AppWeb\ChannelsController::class, 'download'])
                    ->middleware(['auth'])
                    ->name('channels.download');

                // XHR request
                Route::get('/{channel}/view', [AppWeb\ChannelsController::class, 'view'])
                    ->middleware(['auth'])
                    ->name('channels.view');
            });

        // Playlists
        Route::prefix('playlists')
            ->group(function () {
                Route::get('/{playlist}', [AppWeb\PlaylistsController::class, 'show'])
                    ->name('playlists.show');

                // XHR request
                Route::get('/{playlist}/articles', [AppWeb\PlaylistsController::class, 'articles'])
                    ->middleware(['auth'])
                    ->name('playlists.articles');

                // User playlist management
                Route::middleware(['auth'])->group(function () {
                    Route::post('/create', [AppWeb\PlaylistsController::class, 'create'])
                        ->name('playlists.create');

                    Route::put('/{playlist}/update', [AppWeb\PlaylistsController::class, 'update'])
                        ->name('playlists.update');

                    Route::delete('/{playlist}', [AppWeb\PlaylistsController::class, 'destroy'])
                        ->name('playlists.destroy');

                    Route::post('/{playlist}/add-song', [AppWeb\PlaylistsController::class, 'addSong'])
                        ->name('playlists.add-song');

                    Route::delete('/{playlist}/remove-song/{article}', [AppWeb\PlaylistsController::class, 'removeSong'])
                        ->name('playlists.remove-song');

                    Route::put('/{playlist}/reorder', [AppWeb\PlaylistsController::class, 'reorderSongs'])
                        ->name('playlists.reorder');
                });
            });

        // Plays
        Route::middleware(['auth'])
            ->put('plays/article/{article}', [AppWeb\PlaysController::class, 'storeArticlePlay'])
            ->name('plays.article');

        // Following
        Route::middleware(['auth'])
            ->put('followings/toggle/{user}', [AppWeb\FollowingsController::class, 'toggle'])
            ->name('followings.toggle');

        // Like
        Route::prefix('like')
            ->name('like.')
            ->middleware(['auth'])
            ->group(function () {
                Route::put('/channel/{channel}', [AppWeb\LikeController::class, 'channel'])->name('channel');

                Route::put('/article/{article}', [AppWeb\LikeController::class, 'article'])->name('article');

                Route::put('/playlist/{playlist}', [AppWeb\LikeController::class, 'playlist'])->name('playlist');
            });

        Route::prefix('notifications')
            ->name('notifications.')
            ->middleware(['auth'])
            ->group(function () {
                Route::post('/clear', [AppWeb\NotificationsController::class, 'clear'])->name('clear');

                Route::delete('/{notificationId}/destroy', [AppWeb\NotificationsController::class, 'destroy'])->name('destroy');
            });

        // Comments (XHR Request)
        Route::prefix('comments')
            ->middleware(['auth'])
            ->name('comments.')
            ->group(function () {

                Route::put('{comment}/update', [AppWeb\CommentsController::class, 'update'])
                    ->name('update');

                Route::delete('{comment}/delete', [AppWeb\CommentsController::class, 'delete'])
                    ->name('delete');

                Route::prefix('{article}/article')
                    ->group(function () {
                        Route::get('/', [AppWeb\CommentsController::class, 'article'])->name('article');

                        Route::post('/create', [AppWeb\CommentsController::class, 'articleCommentStore'])
                            ->name('article.create');
                    });

                Route::prefix('{channel}/channel')
                    ->group(function () {
                        Route::get('/', [AppWeb\CommentsController::class, 'channel'])->name('channel');

                        Route::post('/create', [AppWeb\CommentsController::class, 'ChannelCommentStore'])
                            ->name('channel.create');
                    });
            });

        // Settings
        Route::prefix('s')
            ->name('settings.')
            ->middleware(['auth'])
            ->group(function () {

                Route::get('/account', AppWeb\Settings\AccountController::class)->name('account');

                // Update auth user image profile
                Route::post('/account/image-profile', [AppWeb\Settings\AccountController::class, 'updateImageProfile'])
                    ->name('account.image-profile');

                Route::post('/artist-request', [AppWeb\Settings\ArtistRequestController::class, 'store'])->name('artist-request');
            });

        // Voting and Competitions
        Route::prefix('voting')
            ->name('voting.')
            ->group(function () {
                Route::get('/', [AppWeb\CompetitionController::class, 'index'])->name('index');

                Route::get('/competition/{competition}/leaderboard', [AppWeb\CompetitionController::class, 'leaderboard'])
                    ->name('leaderboard');

                Route::get('/competition/{competition}/artist/{artist}', [AppWeb\CompetitionController::class, 'artistProfile'])
                    ->name('artist');

                // Artist entry routes (requires auth and artist role)
                Route::middleware(['auth'])
                    ->group(function () {
                        Route::get('/competition/{competition}/entry', [AppWeb\CompetitionController::class, 'entryForm'])
                            ->name('entry.form');

                        Route::post('/competition/{competition}/entry', [AppWeb\CompetitionController::class, 'submitEntry'])
                            ->name('entry.submit');
                    });

                // Voting routes (requires auth)
                Route::middleware(['auth'])
                    ->group(function () {
                        Route::post('/competition/{competition}/vote/{artist}', [AppWeb\PaymentController::class, 'initializePayment'])
                            ->name('vote');

                        Route::get('/competition/{competition}/share/{artist}', [AppWeb\VoteController::class, 'share'])
                            ->name('share');

                        // Payment routes
                        Route::get('/payment/mock', [AppWeb\PaymentController::class, 'mockPaymentPage'])
                            ->name('payment.mock');

                        Route::post('/payment/mock/process', [AppWeb\PaymentController::class, 'processMockPayment'])
                            ->name('payment.mock.process');

                        Route::get('/payment/callback', [AppWeb\PaymentController::class, 'handlePaymentCallback'])
                            ->name('payment.callback');

                        // Subscription routes
                        Route::get('/subscription/checkout', [AppWeb\PaymentController::class, 'subscriptionCheckout'])
                            ->name('subscription.checkout');
                    });
            });

        // Legal Documents
        Route::prefix('legal')
            ->name('legal.')
            ->group(function () {
                Route::get('/documents', [AppWeb\LegalDocumentController::class, 'getActiveDocuments'])
                    ->name('documents');
                Route::get('/document/{type}', [AppWeb\LegalDocumentController::class, 'getDocument'])
                    ->name('document');
                Route::get('/view/{type}', [AppWeb\LegalDocumentController::class, 'showDocument'])
                    ->name('view');
            });

        // User Onboarding
        Route::prefix('user/onboarding')
            ->group(function () {
                Route::get('/status', [OnboardingController::class, 'status']);
                Route::post('/welcome-shown', [OnboardingController::class, 'markWelcomeShown']);
                Route::post('/complete', [OnboardingController::class, 'completeOnboarding']);
            });

        // User Activity
        Route::post('/user/activity/update', [OnboardingController::class, 'updateActivity']);
    });
